import logging
from typing import De<PERSON><PERSON><PERSON><PERSON>
from typing import <PERSON><PERSON>
from uuid import UUID

from slugify import slugify
from woocommerce import API

from src.data.models.woocommerce.category import Category as WCCategory
from src.data.woocommerce_category_datasource import WoocommerceCategoryDatasource
from src.domain.models.proposition import Category
from src.domain.repositories.target_category_repository import TargetCategoryRepository


class WoocommerceCategoryRepositoryImpl(TargetCategoryRepository):
    """
    Repository implementation for syncing categories with WooCommerce.
    Handles upserting categories and ensures slug uniqueness among siblings.
    """

    def __init__(self, woocommerce_api: API, woocommerce_category_datasource: WoocommerceCategoryDatasource):
        """
        Initialize the repository with a WooCommerce API client.
        """
        self._woocommerce_api = woocommerce_api
        self._category_datasource = woocommerce_category_datasource

    def upsert(self, categories: list[Category]) -> None:
        """
        Upsert (create or update) a list of categories in WooCommerce.
        Ensures slugs are unique among siblings (categories with the same parent).
        Also deletes categories that are not present in the upserted list.
        """
        existing_categories = self._category_datasource.get_categories()
        existing_by_guid = {wc.guid: wc for wc in existing_categories}
        used_slugs_by_parent = self._build_used_slugs_by_parent(existing_categories)

        processed_guids = set()

        for category, parent in self._flatten_categories(categories):
            label = self._get_english_label(category)
            parent_guid = parent.guid if parent else None
            parent_id = self._get_parent_id(parent_guid, existing_by_guid)

            existing_wc = existing_by_guid.get(UUID(category.guid))
            if existing_wc:
                self._update_category(existing_wc, category, label, parent_id, existing_by_guid)
                processed_guids.add(existing_wc.guid)
            else:
                wc_category = self._create_category(category, label, parent_id, used_slugs_by_parent, existing_by_guid)
                processed_guids.add(wc_category.guid)

        # Delete categories not in processed_guids, but skip WooCommerce's default 'Uncategorized' category
        for wc in existing_categories:
            is_default = wc.id == 1 or wc.slug == "uncategorized"
            is_untracked = wc.guid is None or wc.guid not in processed_guids
            if is_untracked and not is_default:
                logging.info(f"Deleting category {wc.guid or wc.name} (not present in sync)")
                self._category_datasource.delete(wc.id)

    def _build_used_slugs_by_parent(self, existing_categories: list[WCCategory]) -> DefaultDict[int, set[str]]:
        """
        Build a mapping from parent_id to a set of slugs used by its child categories.
        """
        used_slugs_by_parent: DefaultDict[int, set[str]] = DefaultDict(set)
        for wc in existing_categories:
            used_slugs_by_parent[wc.parent_id].add(wc.slug)
        return used_slugs_by_parent

    def _flatten_categories(self, categories: list[Category]) -> list[Tuple[Category, Category | None]]:
        """
        Flatten a nested list of categories into a list of (category, parent) tuples.
        """

        def recurse(cats, parent=None):
            for cat in cats:
                yield cat, parent
                yield from recurse(cat.sub_categories, cat)

        return list(recurse(categories))

    def _get_english_label(self, category: Category) -> str:
        """
        Get the English label for a category. Raises if not found.
        """
        for t in category.translations:
            if t.language == "en":
                return t.label
        raise ValueError(f"No English translation found for category {category.guid}")

    def _generate_slug(self, category: Category) -> str:
        """
        Generate a slug from the English label of a category.
        """
        return slugify(self._get_english_label(category))

    def _generate_unique_slug(self, category: Category, used_slugs: set[str]) -> str:
        """
        Generate a unique slug for a category among its siblings (same parent).
        Appends -0, -1, ... if needed to avoid collisions.
        """
        base_slug = self._generate_slug(category)
        slug = base_slug
        i = 0
        while slug in used_slugs:
            logging.warning(f"Slug {slug} with {category.guid} already exists, generating unique slug")
            slug = f"{base_slug}-{i}"
            i += 1
        return slug

    def _get_parent_id(self, parent_guid: str | None, existing_by_guid: dict) -> int:
        """
        Get the WooCommerce parent ID for a given parent GUID, or 0 if no parent.
        """
        if parent_guid:
            parent_wc = existing_by_guid.get(UUID(parent_guid))
            return parent_wc.id if parent_wc else 0
        return 0

    def _update_category(
        self, existing_wc: WCCategory, category: Category, label: str, parent_id: int, existing_by_guid: dict
    ) -> None:
        """
        Update a category in WooCommerce if its name, parent, or menu order has changed.
        """
        if (
            existing_wc.name != label
            or existing_wc.parent_id != parent_id
            or existing_wc.menu_order != category.sort_index
        ):
            payload = {
                "name": label,
                "parent": parent_id,
                "menu_order": category.sort_index,
            }
            wc_category = self._category_datasource.update(existing_wc.id, payload)
            existing_by_guid[wc_category.guid] = wc_category

    def _create_category(
        self,
        category: Category,
        label: str,
        parent_id: int,
        used_slugs_by_parent: DefaultDict[int, set[str]],
        existing_by_guid: dict,
    ) -> WCCategory:
        """
        Create a new category in WooCommerce, ensuring a unique slug among siblings.
        Returns the created WCCategory.
        """
        used_slugs = used_slugs_by_parent[parent_id]
        slug = self._generate_unique_slug(category, used_slugs)
        used_slugs.add(slug)
        logging.info(f"Creating category {category.guid} with parent {parent_id}")
        payload = {
            "name": label,
            "slug": slug,
            "parent": parent_id,
            "guid": category.guid,
            "menu_order": category.sort_index,
        }
        wc_category = self._category_datasource.create(payload)
        existing_by_guid[wc_category.guid] = wc_category
        return wc_category
